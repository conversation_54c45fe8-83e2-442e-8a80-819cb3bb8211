In the order summary card on the OrderPage component, implement the following features:

**1. Item Management Functions:**
- Add ability for users to delete individual selected items from their current order
- Add ability to delete an entire order row (cancel the whole order)
- Distinguish between "editing an order" (removing individual items) vs "canceling an order" (removing the entire order)

**2. History Icon and Dialog:**
- Add a history icon on the right side of the order summary card
- When clicked, open a modal dialog that displays the order history
- The dialog should show a chronological timeline of all order activities for the current customer

**3. History Data Requirements:**
- Retrieve history data based on the customer name stored in localStorage (using the existing useCustomerName hook)
- Track and display the following events with timestamps:
  - Order placement: "nmcong1 đã đặt món lúc 12:00"
  - Item removal/editing: "nmcong1 đã xoá món [item_name] lúc 12:03"  
  - Order cancellation: "nmcong1 đã huỷ đặt món lúc 12:05"

**4. Data Storage:**
- Review current database schema to ensure it meets the requirements for history tracking
- Create new database table(if needed) to store all history events with timestamps (if needed)
- Associate history entries with the customer name for persistence across browser sessions
- Implement proper data structure to track different types of actions (place order, edit order, cancel order)

**5. UI/UX Requirements:**
- Use consistent Vietnamese language for all messages
- Display history in reverse chronological order (newest first)
- Include proper icons for different action types
- Follow the existing design patterns and styling used in the application
- Ensure the history dialog is responsive and accessible

**Technical Notes:**
- Extend the existing localStorage utilities to handle history tracking
- Use the project's camelCase naming convention
- Implement proper error handling for localStorage operations
- Consider using the existing custom hooks pattern for history management