import { useState, useEffect, useCallback } from "react";
import { orderHistoryService, OrderHistoryEvent, OrderHistoryActionType, AddHistoryEventParams } from "@/services/orderHistoryService";

export interface UseOrderHistoryState {
  events: OrderHistoryEvent[];
  isLoading: boolean;
  error: string | null;
}

export interface UseOrderHistoryReturn extends UseOrderHistoryState {
  addEvent: (params: Omit<AddHistoryEventParams, 'userName'>) => Promise<boolean>;
  refreshHistory: () => Promise<void>;
  clearError: () => void;
  clearHistory: () => void;
}

/**
 * Custom hook for managing order history for a specific user
 * Provides loading states, error handling, and automatic data management
 */
export function useOrderHistory(userName: string): UseOrderHistoryReturn {
  const [state, setState] = useState<UseOrderHistoryState>({
    events: [],
    isLoading: false,
    error: null,
  });

  /**
   * Fetch history events for the current user
   */
  const fetchHistory = useCallback(async () => {
    if (!userName.trim()) {
      setState(prev => ({ ...prev, events: [], isLoading: false, error: null }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const result = await orderHistoryService.getHistoryForUser(userName);

      if (result.success) {
        setState(prev => ({
          ...prev,
          events: result.events || [],
          isLoading: false,
          error: null,
        }));
      } else {
        setState(prev => ({
          ...prev,
          events: [],
          isLoading: false,
          error: result.error || "Failed to fetch history",
        }));
      }
    } catch (error) {
      console.error("❌ Error in fetchHistory:", error);
      setState(prev => ({
        ...prev,
        events: [],
        isLoading: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      }));
    }
  }, [userName]);

  /**
   * Add a new history event
   */
  const addEvent = useCallback(async (params: Omit<AddHistoryEventParams, 'userName'>): Promise<boolean> => {
    if (!userName.trim()) {
      console.warn("Cannot add history event: userName is empty");
      return false;
    }

    try {
      const fullParams: AddHistoryEventParams = {
        ...params,
        userName,
      };

      const result = await orderHistoryService.addHistoryEvent(fullParams);

      if (result.success && result.event) {
        // Add the new event to the current state
        setState(prev => ({
          ...prev,
          events: [result.event!, ...prev.events],
          error: null,
        }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          error: result.error || "Failed to add history event",
        }));
        return false;
      }
    } catch (error) {
      console.error("❌ Error adding history event:", error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      }));
      return false;
    }
  }, [userName]);

  /**
   * Refresh history data
   */
  const refreshHistory = useCallback(async () => {
    await fetchHistory();
  }, [fetchHistory]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * Clear all history (for testing)
   */
  const clearHistory = useCallback(() => {
    orderHistoryService.clearAllHistory();
    setState(prev => ({ ...prev, events: [], error: null }));
  }, []);

  /**
   * Fetch history when userName changes
   */
  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  return {
    ...state,
    addEvent,
    refreshHistory,
    clearError,
    clearHistory,
  };
}

/**
 * Helper hook to add common history events with pre-formatted descriptions
 */
export function useOrderHistoryActions(userName: string) {
  const { addEvent } = useOrderHistory(userName);

  const addOrderPlacedEvent = useCallback(async (orderId?: string, totalAmount?: number) => {
    const description = orderHistoryService.generateDescription("order_placed", userName);
    return await addEvent({
      actionType: "order_placed",
      description,
      orderId,
      totalAmount,
    });
  }, [addEvent, userName]);

  const addItemRemovedEvent = useCallback(async (itemName: string, itemQuantity?: number) => {
    const description = orderHistoryService.generateDescription("item_removed", userName, itemName);
    return await addEvent({
      actionType: "item_removed",
      description,
      itemName,
      itemQuantity,
    });
  }, [addEvent, userName]);

  const addOrderCancelledEvent = useCallback(async (orderId?: string) => {
    const description = orderHistoryService.generateDescription("order_cancelled", userName);
    return await addEvent({
      actionType: "order_cancelled",
      description,
      orderId,
    });
  }, [addEvent, userName]);

  return {
    addOrderPlacedEvent,
    addItemRemovedEvent,
    addOrderCancelledEvent,
  };
}
