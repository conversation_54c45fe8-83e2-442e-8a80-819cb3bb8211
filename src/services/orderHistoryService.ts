import { supabase } from "@/integrations/supabase/client";
import { Database, TablesInsert } from "@/integrations/supabase/types";

// Type aliases for better readability
type OrderHistoryInsert = TablesInsert<"order_history">;
type OrderHistoryRow = Database["public"]["Tables"]["order_history"]["Row"];

export type OrderHistoryActionType = "order_placed" | "item_removed" | "order_cancelled";

export interface OrderHistoryEvent {
  id: string;
  userName: string;
  actionType: OrderHistoryActionType;
  orderId?: string;
  itemName?: string;
  itemQuantity?: number;
  totalAmount?: number;
  description: string;
  createdAt: string;
}

export interface AddHistoryEventParams {
  userName: string;
  actionType: OrderHistoryActionType;
  description: string;
  orderId?: string;
  itemName?: string;
  itemQuantity?: number;
  totalAmount?: number;
}

export interface GetHistoryResult {
  success: boolean;
  events?: OrderHistoryEvent[];
  error?: string;
}

export interface AddHistoryResult {
  success: boolean;
  event?: OrderHistoryEvent;
  error?: string;
}

const HISTORY_STORAGE_KEY = "order_history_events";

/**
 * Service for managing order history events
 * Supports both Supabase database and localStorage fallback
 */
export const orderHistoryService = {
  /**
   * Add a new history event
   */
  async addHistoryEvent(params: AddHistoryEventParams): Promise<AddHistoryResult> {
    try {
      // Try to save to Supabase first
      const historyInsert: OrderHistoryInsert = {
        user_name: params.userName,
        action_type: params.actionType,
        description: params.description,
        order_id: params.orderId || null,
        item_name: params.itemName || null,
        item_quantity: params.itemQuantity || null,
        total_amount: params.totalAmount || null,
      };

      const { data: newEvent, error } = await supabase
        .from("order_history")
        .insert(historyInsert)
        .select()
        .single();

      if (error) {
        console.warn("Failed to save to Supabase, falling back to localStorage:", error);
        return this.addHistoryEventToLocalStorage(params);
      }

      if (!newEvent) {
        throw new Error("No data returned from Supabase insert");
      }

      const event: OrderHistoryEvent = {
        id: newEvent.id,
        userName: newEvent.user_name,
        actionType: newEvent.action_type as OrderHistoryActionType,
        orderId: newEvent.order_id || undefined,
        itemName: newEvent.item_name || undefined,
        itemQuantity: newEvent.item_quantity || undefined,
        totalAmount: newEvent.total_amount || undefined,
        description: newEvent.description,
        createdAt: newEvent.created_at || new Date().toISOString(),
      };

      // Also save to localStorage for backup
      this.addHistoryEventToLocalStorage(params);

      console.log("✅ History event saved to Supabase:", event);
      return { success: true, event };
    } catch (error) {
      console.error("❌ Error saving history event:", error);
      // Fallback to localStorage
      return this.addHistoryEventToLocalStorage(params);
    }
  },

  /**
   * Get history events for a specific user
   */
  async getHistoryForUser(userName: string): Promise<GetHistoryResult> {
    try {
      // Try to fetch from Supabase first
      const { data: events, error } = await supabase
        .from("order_history")
        .select("*")
        .eq("user_name", userName)
        .is("deleted_at", null)
        .order("created_at", { ascending: false });

      if (error) {
        console.warn("Failed to fetch from Supabase, falling back to localStorage:", error);
        return this.getHistoryFromLocalStorage(userName);
      }

      const transformedEvents: OrderHistoryEvent[] = (events || []).map(event => ({
        id: event.id,
        userName: event.user_name,
        actionType: event.action_type as OrderHistoryActionType,
        orderId: event.order_id || undefined,
        itemName: event.item_name || undefined,
        itemQuantity: event.item_quantity || undefined,
        totalAmount: event.total_amount || undefined,
        description: event.description,
        createdAt: event.created_at || new Date().toISOString(),
      }));

      console.log(`✅ Fetched ${transformedEvents.length} history events for ${userName}`);
      return { success: true, events: transformedEvents };
    } catch (error) {
      console.error("❌ Error fetching history events:", error);
      // Fallback to localStorage
      return this.getHistoryFromLocalStorage(userName);
    }
  },

  /**
   * Fallback: Add history event to localStorage
   */
  addHistoryEventToLocalStorage(params: AddHistoryEventParams): AddHistoryResult {
    try {
      const event: OrderHistoryEvent = {
        id: `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        userName: params.userName,
        actionType: params.actionType,
        orderId: params.orderId,
        itemName: params.itemName,
        itemQuantity: params.itemQuantity,
        totalAmount: params.totalAmount,
        description: params.description,
        createdAt: new Date().toISOString(),
      };

      const existingEvents = this.getLocalStorageEvents();
      const updatedEvents = [event, ...existingEvents];
      
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(updatedEvents));
      
      console.log("✅ History event saved to localStorage:", event);
      return { success: true, event };
    } catch (error) {
      console.error("❌ Error saving to localStorage:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Fallback: Get history events from localStorage
   */
  getHistoryFromLocalStorage(userName: string): GetHistoryResult {
    try {
      const allEvents = this.getLocalStorageEvents();
      const userEvents = allEvents
        .filter(event => event.userName === userName)
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      console.log(`✅ Fetched ${userEvents.length} history events from localStorage for ${userName}`);
      return { success: true, events: userEvents };
    } catch (error) {
      console.error("❌ Error reading from localStorage:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Helper: Get all events from localStorage
   */
  getLocalStorageEvents(): OrderHistoryEvent[] {
    try {
      const eventsJson = localStorage.getItem(HISTORY_STORAGE_KEY);
      if (!eventsJson) return [];
      
      return JSON.parse(eventsJson);
    } catch (error) {
      console.warn("Error parsing localStorage events:", error);
      return [];
    }
  },

  /**
   * Clear all history events (for testing)
   */
  clearAllHistory(): void {
    localStorage.removeItem(HISTORY_STORAGE_KEY);
  },

  /**
   * Generate formatted description for different action types
   */
  generateDescription(actionType: OrderHistoryActionType, userName: string, itemName?: string): string {
    const now = new Date();
    const timeString = now.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });

    switch (actionType) {
      case "order_placed":
        return `${userName} đã đặt món lúc ${timeString}`;
      case "item_removed":
        return `${userName} đã xoá món ${itemName || 'không xác định'} lúc ${timeString}`;
      case "order_cancelled":
        return `${userName} đã huỷ đặt món lúc ${timeString}`;
      default:
        return `${userName} đã thực hiện hành động lúc ${timeString}`;
    }
  },
};
