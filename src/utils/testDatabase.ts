import { supabase } from "@/integrations/supabase/client";
import { supabaseOrderService } from "@/services/supabaseOrderService";

/**
 * Test database connection and basic operations
 */
export const testDatabase = async () => {
  console.log("🔍 Testing database connection...");

  try {
    // Test 1: Basic connection
    const { data: testData, error: testError } = await supabase
      .from("users")
      .select("count", { count: "exact", head: true });

    if (testError) {
      console.error("❌ Database connection failed:", testError);
      return { success: false, error: testError.message };
    }

    console.log("✅ Database connection successful");

    // Test 2: Check if we have any orders
    const ordersResult = await supabaseOrderService.getAllOrders();
    if (ordersResult.success) {
      console.log(`📊 Found ${ordersResult.orders?.length || 0} orders in database`);
    } else {
      console.error("❌ Failed to fetch orders:", ordersResult.error);
    }

    // Test 3: Check if we have any dish summary data
    const dishSummaryResult = await supabaseOrderService.getDishSummary();
    if (dishSummaryResult.success) {
      console.log(`🍽️ Found ${dishSummaryResult.dishSummary?.length || 0} dishes in summary`);
    } else {
      console.error("❌ Failed to fetch dish summary:", dishSummaryResult.error);
    }

    return { success: true };
  } catch (error) {
    console.error("❌ Unexpected error:", error);
    return { success: false, error: String(error) };
  }
};

// Make it available globally for testing
(window as any).testDatabase = testDatabase;
