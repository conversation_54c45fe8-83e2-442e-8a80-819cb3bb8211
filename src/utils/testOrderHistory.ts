import { orderHistoryService } from "@/services/orderHistoryService";

/**
 * Test utility for order history functionality
 */
export const testOrderHistory = {
  /**
   * Add sample history events for testing
   */
  async addSampleHistory(userName: string = "nmcong1") {
    console.log(`🧪 Adding sample history events for ${userName}...`);

    const events = [
      {
        userName,
        actionType: "order_placed" as const,
        description: orderHistoryService.generateDescription("order_placed", userName),
        totalAmount: 550,
      },
      {
        userName,
        actionType: "item_removed" as const,
        description: orderHistoryService.generateDescription("item_removed", userName, "唐揚げ"),
        itemName: "唐揚げ",
        itemQuantity: 1,
      },
      {
        userName,
        actionType: "order_cancelled" as const,
        description: orderHistoryService.generateDescription("order_cancelled", userName),
      },
      {
        userName,
        actionType: "order_placed" as const,
        description: orderHistoryService.generateDescription("order_placed", userName),
        totalAmount: 720,
      },
    ];

    try {
      for (const event of events) {
        const result = await orderHistoryService.addHistoryEvent(event);
        if (result.success) {
          console.log(`✅ Added: ${event.description}`);
        } else {
          console.error(`❌ Failed to add: ${event.description}`, result.error);
        }
        // Add small delay between events
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log("🎉 Sample history events added successfully!");
      return { success: true };
    } catch (error) {
      console.error("❌ Error adding sample history:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test fetching history for a user
   */
  async testFetchHistory(userName: string = "nmcong1") {
    console.log(`🔍 Testing history fetch for ${userName}...`);

    try {
      const result = await orderHistoryService.getHistoryForUser(userName);
      
      if (result.success) {
        console.log(`✅ Fetched ${result.events?.length || 0} events:`, result.events);
        return { success: true, events: result.events };
      } else {
        console.error("❌ Failed to fetch history:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Error fetching history:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test all history functionality
   */
  async runFullTest(userName: string = "nmcong1") {
    console.log("🧪 Running full order history test...");

    // Clear existing history
    orderHistoryService.clearAllHistory();
    console.log("🧹 Cleared existing history");

    // Add sample events
    const addResult = await this.addSampleHistory(userName);
    if (!addResult.success) {
      return addResult;
    }

    // Fetch and verify
    const fetchResult = await this.testFetchHistory(userName);
    if (!fetchResult.success) {
      return fetchResult;
    }

    console.log("✅ Full order history test completed successfully!");
    return { success: true, eventsCount: fetchResult.events?.length || 0 };
  },

  /**
   * Clear all history data
   */
  clearHistory() {
    orderHistoryService.clearAllHistory();
    console.log("🧹 All history data cleared");
  },
};

// Make it available globally for testing
(window as any).testOrderHistory = testOrderHistory;

// Auto-run a basic test
setTimeout(() => {
  console.log("🚀 Order history test utility loaded. Use testOrderHistory.runFullTest() to test.");
}, 1000);
