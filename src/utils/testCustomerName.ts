/**
 * Test utilities for customer name persistence functionality
 * These functions can be called from the browser console for testing
 */

const CUSTOMER_NAME_KEY = "food-order-customer-name";

export const testCustomerName = {
  /**
   * Test localStorage functionality
   */
  testLocalStorage() {
    console.log("🧪 Testing localStorage functionality...");
    
    try {
      // Test if localStorage is available
      if (typeof window === "undefined" || !window.localStorage) {
        console.error("❌ localStorage is not available");
        return { success: false, error: "localStorage not available" };
      }

      // Test setting and getting values
      const testValue = `test-${Date.now()}`;
      localStorage.setItem(CUSTOMER_NAME_KEY, testValue);
      const retrieved = localStorage.getItem(CUSTOMER_NAME_KEY);

      if (retrieved === testValue) {
        console.log("✅ localStorage test successful");
        localStorage.removeItem(CUSTOMER_NAME_KEY); // Clean up
        return { success: true };
      } else {
        console.error("❌ localStorage test failed - value mismatch");
        return { success: false, error: "Value mismatch" };
      }
    } catch (error) {
      console.error("❌ localStorage test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Set a test customer name
   */
  setTestCustomerName(name: string = "Test User") {
    console.log(`🧪 Setting test customer name: "${name}"`);
    
    try {
      localStorage.setItem(CUSTOMER_NAME_KEY, name);
      console.log("✅ Test customer name set successfully");
      return { success: true, name };
    } catch (error) {
      console.error("❌ Failed to set test customer name:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Get the current stored customer name
   */
  getStoredCustomerName() {
    console.log("🧪 Getting stored customer name...");
    
    try {
      const stored = localStorage.getItem(CUSTOMER_NAME_KEY);
      console.log(`Current stored name: "${stored}"`);
      return { success: true, name: stored };
    } catch (error) {
      console.error("❌ Failed to get stored customer name:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Clear the stored customer name
   */
  clearStoredCustomerName() {
    console.log("🧪 Clearing stored customer name...");
    
    try {
      localStorage.removeItem(CUSTOMER_NAME_KEY);
      console.log("✅ Stored customer name cleared");
      return { success: true };
    } catch (error) {
      console.error("❌ Failed to clear stored customer name:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test the complete customer name persistence flow
   */
  testPersistenceFlow() {
    console.log("🧪 Testing complete customer name persistence flow...");
    
    const results = {
      localStorage: this.testLocalStorage(),
      clearInitial: this.clearStoredCustomerName(),
      checkEmpty: this.getStoredCustomerName(),
      setName: this.setTestCustomerName("John Doe"),
      getName: this.getStoredCustomerName(),
      updateName: this.setTestCustomerName("Jane Smith"),
      getUpdated: this.getStoredCustomerName(),
      clearFinal: this.clearStoredCustomerName(),
      checkCleared: this.getStoredCustomerName(),
    };

    console.log("📊 Persistence Flow Test Results:", results);
    
    const allPassed = Object.values(results).every(result => result.success);
    
    if (allPassed) {
      console.log("🎉 All persistence tests passed!");
    } else {
      console.log("⚠️ Some persistence tests failed. Check the results above.");
    }
    
    return results;
  },

  /**
   * Simulate the order confirmation flow
   */
  simulateOrderFlow() {
    console.log("🧪 Simulating order confirmation flow...");
    
    try {
      // Step 1: Clear any existing name
      this.clearStoredCustomerName();
      
      // Step 2: Check that dialog would show empty field
      const initial = this.getStoredCustomerName();
      console.log("Initial state (should be empty):", initial.name);
      
      // Step 3: Simulate user entering name and successful order
      const customerName = "Test Customer";
      this.setTestCustomerName(customerName);
      console.log(`Simulated successful order for: ${customerName}`);
      
      // Step 4: Check that name is now stored
      const stored = this.getStoredCustomerName();
      console.log("After order (should be stored):", stored.name);
      
      // Step 5: Simulate opening dialog again (should auto-fill)
      console.log("Next dialog opening would auto-fill with:", stored.name);
      
      if (stored.name === customerName) {
        console.log("✅ Order flow simulation successful");
        return { success: true, finalName: stored.name };
      } else {
        console.error("❌ Order flow simulation failed - name not persisted");
        return { success: false, error: "Name not persisted correctly" };
      }
    } catch (error) {
      console.error("❌ Order flow simulation failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Run all customer name tests
   */
  runAllTests() {
    console.log("🚀 Running all customer name persistence tests...");
    
    const results = {
      persistenceFlow: this.testPersistenceFlow(),
      orderFlow: this.simulateOrderFlow(),
    };

    console.log("📊 All Customer Name Test Results:", results);
    
    const allPassed = Object.values(results).every(result => result.success);
    
    if (allPassed) {
      console.log("🎉 All customer name tests passed!");
    } else {
      console.log("⚠️ Some customer name tests failed. Check the results above.");
    }
    
    return results;
  },
};

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testCustomerName = testCustomerName;
}
