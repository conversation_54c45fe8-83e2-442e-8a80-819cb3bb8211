import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, Users, ShoppingBag, RefreshCw, AlertCircle } from "lucide-react";
import { useOrderManagement } from "@/hooks/useOrderManagement";

const chipColors = [
  "bg-blue-100 text-blue-800",
  "bg-green-100 text-green-800", 
  "bg-purple-100 text-purple-800",
  "bg-orange-100 text-orange-800",
  "bg-pink-100 text-pink-800",
  "bg-yellow-100 text-yellow-800",
  "bg-indigo-100 text-indigo-800",
  "bg-red-100 text-red-800",
];

export function OrderManagementPage() {
  const { orders, dishSummary, isLoading, error, totalOrders, totalDishes, refetch, clearError } = useOrderManagement();

  const formatPrice = (price: number) => `¥${price.toLocaleString()}`;

  const getChipColor = (index: number) => {
    return chipColors[index % chipColors.length];
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-warm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground">Đang tải dữ liệu đơn hàng...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            📋 Quản Lý Đơn Đặt
          </h1>
          <p className="text-muted-foreground">
            Theo dõi và quản lý tất cả đơn đặt cơm của công ty
          </p>
          <div className="flex justify-center">
            <Button
              onClick={refetch}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Làm mới dữ liệu
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div className="flex-1">
                  <p className="text-red-800 font-medium">Lỗi tải dữ liệu</p>
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
                <Button
                  onClick={clearError}
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:text-red-800"
                >
                  Đóng
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-blue-200 p-3 rounded-lg">
                  <Users className="h-6 w-6 text-blue-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-600">Tổng đơn hàng</p>
                  <p className="text-2xl font-bold text-blue-800">{totalOrders}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-emerald-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-green-200 p-3 rounded-lg">
                  <ShoppingBag className="h-6 w-6 text-green-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-600">Tổng món ăn</p>
                  <p className="text-2xl font-bold text-green-800">{totalDishes}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Orders Table */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">📋 Đơn đặt của người dùng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="font-semibold">Tên user</TableHead>
                    <TableHead className="font-semibold">Món đã đặt</TableHead>
                    <TableHead className="font-semibold">Tổng tiền</TableHead>
                    <TableHead className="font-semibold">Trạng thái</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                        Chưa có đơn hàng nào
                      </TableCell>
                    </TableRow>
                  ) : (
                    orders.map((order) => (
                      <TableRow key={order.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{order.userName}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {order.dishes.length === 0 ? (
                              <span className="text-muted-foreground text-sm">Không có món</span>
                            ) : (
                              order.dishes.map((dish, index) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className={`text-xs ${getChipColor(index)} border-0`}
                                >
                                  {dish}
                                </Badge>
                              ))
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="font-semibold text-primary">
                          {formatPrice(order.totalAmount)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {order.isPaid ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-green-700 font-medium">Đã thanh toán</span>
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 text-red-600" />
                                <span className="text-red-700 font-medium">Chưa thanh toán</span>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Dish Summary Table */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">📊 Tổng hợp món đã đặt</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="font-semibold">Tên sản phẩm</TableHead>
                    <TableHead className="font-semibold">Số lượng</TableHead>
                    <TableHead className="font-semibold">Tổng tiền</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dishSummary.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                        Chưa có dữ liệu món ăn
                      </TableCell>
                    </TableRow>
                  ) : (
                    dishSummary
                      .sort((a, b) => b.quantity - a.quantity)
                      .map((dish, index) => (
                        <TableRow key={index} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{dish.dishName}</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                              {dish.quantity}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-semibold text-primary">
                            {formatPrice(dish.totalRevenue)}
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}